# 腾云法智-web

## 项目简介
舆情监控系统，实时监控互联网各大平台的八卦新闻信息，支持预警推送等功能。

## 项目启动与发布

| 操作         | 命令/地址                  |
|--------------|---------------------------|
| 安装依赖     | `npm install`             |
| 启动开发服务 | `npm run dev`             |
| 浏览器访问  | http://localhost:80    |
| 构建测试环境  | npm run build:stage    |
| 构建生产环境  | npm run build:prod    |


## 相关资料

| 名称 | 地址 |
|----------|----------|
| 若依前端手册    | https://doc.ruoyi.vip/ruoyi-vue/document/qdsc.html   |
| 组件文档    | https://doc.ruoyi.vip/ruoyi-vue/document/zjwd.html#%E5%9F%BA%E7%A1%80%E6%A1%86%E6%9E%B6%E7%BB%84%E4%BB%B6   |
