<!-- FilterTagManager.vue -->
<template>
  <div class="filter-tag-manager-container">
    <div class="tags-and-more-wrapper">
      <div class="tags-and-more-on-same-line">
        <draggable
          ref="visibleTagsContainerRef"
          v-model="visibleTagsData"
          class="tag-list-container visible-tags-container"
          v-bind="dragOptions"
          @end="onDragEnd($event, 'visible')"
        >
          <!-- 循环渲染的普通标签 -->
          <el-button
            v-for="tag in visibleTagsData"
            :key="tag.id"
            :data-tag-id="tag.id"
            :type="tag.id === selectedTagId ? 'primary' : 'default'"
            class="tag-btn"
            size="mini"
            @click="handleTagClick(tag)"
          >
            <span class="tag-text">{{ tag.name }}</span>
            <span class="tag-close" @click.stop="handleVisibleTagAction(tag)">×</span>
          </el-button>

          <!-- “更多”按钮被移到这里，作为draggable的最后一个非拖拽元素 -->
          <el-dropdown
            v-if="moreTagsData.length > 0"
            slot="footer"
            ref="moreDropdownRef"
            placement="bottom-start"
            trigger="click"
            class="more-tags-dropdown-container"
            @visible-change="handleMoreDropdownVisibleChange"
          >
            <el-button size="mini" class="more-tags-btn">
              更多 <i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown" class="more-tags-dropdown-menu">
              <div class="dropdown-draggable-wrapper">
                <draggable
                  v-model="moreTagsData"
                  v-bind="dragOptions"
                  tag="div"
                  class="more-tags-draggable-list"
                  @end="onDragEnd($event, 'more')"
                >
                  <el-dropdown-item
                    v-for="tag in moreTagsData"
                    :key="tag.id"
                    :data-tag-id="tag.id"
                    :command="tag"
                    class="tag-btn-dropdown-item"
                    :class="{ 'is-selected': tag.id === selectedTagId }"
                    @click.native="handleMoreTagClick(tag)"
                  >
                    <span class="tag-name-in-dropdown">{{ tag.name }}</span>
                    <span class="tag-close-dropdown" @click.stop="handleDeleteTagDirectly(tag)">×</span>
                  </el-dropdown-item>
                </draggable>
              </div>
            </el-dropdown-menu>
          </el-dropdown>

          <!-- 空状态提示 -->
          <div v-if="visibleTagsData.length === 0 && allTagsLocal.length > 0" slot="footer" class="empty-draggable-area-inline">
            从"更多"点击标签到此固定
          </div>
          <div v-if="allTagsLocal.length === 0" slot="footer" class="empty-draggable-area-inline">
            (暂无标签)
          </div>
        </draggable>
      </div>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { deleteFilterWord } from '@/api/yqmonitor'

function debounce(fn, delay) {
  let timer = null
  return function(...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export default {
  name: 'FilterTagManager',
  components: {
    draggable
  },
  props: {
    filterTags: {
      type: Array,
      default: () => []
    },
    maxVisibleTags: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      selectedTagId: '',
      allTagsLocal: [],
      visibleTagsData: [],
      moreTagsData: [],
      dragOptions: {
        animation: 200,
        ghostClass: 'ghost-tag',
        fallbackOnBody: true,
        swapThreshold: 0.65,
        preventOnFilter: false
      },
      isMoreDropdownVisible: false,
      debouncedAdjust: null
    }
  },
  watch: {
    filterTags: {
      // **核心修复1：移除 immediate: true**
      // 现在只负责响应后续的数据更新
      handler(newVal) {
        this.allTagsLocal = JSON.parse(JSON.stringify(newVal || []))
        this.normalizeAndEmitChanges(false)
      },
      deep: true
    }
  },
  mounted() {
    // **核心修复2：在 mounted 中处理初始数据和第一次计算**
    // 此时 DOM 已稳定，可以安全地进行测量
    this.allTagsLocal = JSON.parse(JSON.stringify(this.filterTags || []))
    this.normalizeAndEmitChanges(false)

    this.debouncedAdjust = debounce(() => this.normalizeAndEmitChanges(false), 150)
    window.addEventListener('resize', this.debouncedAdjust)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.debouncedAdjust)
  },
  methods: {
    adjustVisibleTags() {
      return new Promise(resolve => {
        const allSortedTags = [...this.allTagsLocal].sort((a, b) => a.order - b.order);
        const maxCandidates = allSortedTags.slice(0, this.maxVisibleTags);

        const checkAndAdjust = (currentCandidates) => {
          this.visibleTagsData = currentCandidates;
          const hasMoreButton = allSortedTags.length > currentCandidates.length;

          this.$nextTick(() => {
            const container = this.$refs.visibleTagsContainerRef?.$el;
            if (!container || !container.children.length) {
              resolve(currentCandidates.length);
              return;
            }

            const tagElements = Array.from(container.children).filter(el => el.classList.contains('tag-btn'));
            const moreButtonElement = hasMoreButton ? this.$refs.moreDropdownRef?.$el : null;
            const allElementsToMeasure = moreButtonElement ? [...tagElements, moreButtonElement] : tagElements;
            
            if (allElementsToMeasure.length === 0) {
              resolve(0);
              return;
            }

            const firstRowOffsetTop = allElementsToMeasure[0].offsetTop;
            let hasThirdRow = false;
            let secondRowOffsetTop = -1;

            for (const el of allElementsToMeasure) {
              if (secondRowOffsetTop === -1 && el.offsetTop > firstRowOffsetTop) {
                secondRowOffsetTop = el.offsetTop;
              } else if (secondRowOffsetTop !== -1 && el.offsetTop > secondRowOffsetTop) {
                hasThirdRow = true;
                break;
              }
            }
            
            if (hasThirdRow && currentCandidates.length > 0) {
              checkAndAdjust(currentCandidates.slice(0, -1));
            } else {
              resolve(currentCandidates.length);
            }
          });
        };
        
        checkAndAdjust(maxCandidates);
      });
    },

    recalculateDraggableLists() {
      const pinned = this.allTagsLocal.filter(t => t.isPinned).sort((a, b) => a.order - b.order)
      const unpinned = this.allTagsLocal.filter(t => !t.isPinned).sort((a, b) => a.order - b.order)
      
      this.allTagsLocal = [
        ...pinned.map((tag, index) => ({ ...tag, order: index, isPinned: true })),
        ...unpinned.map((tag, index) => ({ ...tag, order: pinned.length + index, isPinned: false }))
      ]

      this.visibleTagsData = this.allTagsLocal.filter(t => t.isPinned)
      this.moreTagsData = this.allTagsLocal.filter(t => !t.isPinned)
    },
    handleTagClick(tag) {
      console.log('1. [FilterTagManager] Setting selectedTagId to:', tag.id); 
      this.selectedTagId = tag.id
      this.$emit('tag-selected', tag)
    },
    handleMoreTagClick(tagToPin) {
      const targetTag = this.allTagsLocal.find(t => t.id === tagToPin.id)
      if (!targetTag) return

      if (this.visibleTagsData.length > 0) {
        const lastVisibleTag = this.visibleTagsData[this.visibleTagsData.length - 1]
        const tagToUnpin = this.allTagsLocal.find(t => t.id === lastVisibleTag.id)
        if (tagToUnpin) {
          tagToUnpin.isPinned = false
        }
      }

      targetTag.isPinned = true
      
      this.normalizeAndEmitChanges()

      if (this.$refs.moreDropdownRef) {
        this.$refs.moreDropdownRef.hide()
      }
    },
    async handleVisibleTagAction(tag) {
      try {
        await this.$confirm(null, `操作标签 "${tag.name}"`, {
          distinguishCancelAndClose: true,
          confirmButtonText: '移到更多',
          cancelButtonText: '删除标签',
          type: 'info',
          center: true
        })
        const targetTag = this.allTagsLocal.find(t => t.id === tag.id)
        if (targetTag) {
          targetTag.isPinned = false
        }
        this.normalizeAndEmitChanges()
      } catch (action) {
        if (action === 'cancel') {
          this.$nextTick(async() => {
            await this.handleDeleteTagDirectly(tag)
          })
        } else if (action === 'close') {
          // 用户点击了右上角的关闭按钮或按了 ESC，不做任何事
        } else {
          console.error('Unexpected rejection from $confirm in handleVisibleTagAction:', action)
        }
      }
    },
    async handleDeleteTagDirectly(tagToDelete) {
      try {
        await this.$confirm(`确定要永久删除标签 "${tagToDelete.name}" 吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteFilterWord({
          userId: tagToDelete.userId,
          menuId: tagToDelete.menuId,
          name: tagToDelete.name
        })
        this.$message.success('标签删除成功')
        if (this.selectedTagId === tagToDelete.id) {
          this.selectedTagId = ''
        }
        this.$emit('tags-updated-by-delete')
      } catch (e) {
        if (e !== 'cancel') {
          this.$message.error('删除标签失败')
          console.error('Error deleting tag:', e)
        }
      }
    },
    onDragEnd(event, listType) {
      let changedList
      if (listType === 'visible') {
        changedList = this.visibleTagsData
      } else {
        changedList = this.moreTagsData
      }
      changedList.forEach((draggedTag, newIndexInList) => {
        const matchInAll = this.allTagsLocal.find(t => t.id === draggedTag.id)
        if (matchInAll) {
          if (listType === 'visible') {
            matchInAll.order = newIndexInList
          } else {
            matchInAll.order = this.visibleTagsData.length + newIndexInList
          }
        }
      })
      this.normalizeAndEmitChanges()
    },
    async normalizeAndEmitChanges(emitChanges = true) {
      const fitCount = await this.adjustVisibleTags();
      
      const allSortedTags = [...this.allTagsLocal].sort((a, b) => a.order - b.order);
      const fitIds = new Set(allSortedTags.slice(0, fitCount).map(t => t.id));

      this.allTagsLocal.forEach(tag => {
        tag.isPinned = fitIds.has(tag.id);
      });
      
      this.recalculateDraggableLists()
      
      if (emitChanges) {
        this.$nextTick(() => {
          this.$emit('tags-config-changed', [...this.allTagsLocal])
        })
      }
    },
    resetSelectedTagState() {
      this.selectedTagId = ''
    },
    setSelectedTagState(tagId) {
      this.selectedTagId = tagId
    },
    getSelectedTagId() {
      return this.selectedTagId
    },
    handleMoreDropdownVisibleChange(visible) {
      this.isMoreDropdownVisible = visible
    }
  }
}
</script>

<style lang="scss" scoped>
/* 样式部分保持不变 */
.filter-tag-manager-container {
  width: 100%;
  padding: 8px 0;
  /* 给上下一点间距 */
}
.tags-and-more-wrapper {
  width: 100%;
}
.tags-and-more-on-same-line {
  /* 这个class现在只是一个简单的包裹层，不再需要flex布局 */
}
.visible-tags-container {
  display: flex;
  flex-wrap: wrap; /* 允许标签换行 */
  gap: 8px; /* 标签之间的间距 */
  /* 不再需要固定高度，由内容和外部容器决定 */
  align-items: center; 
  min-width: 0;
}
.empty-draggable-area-inline {
  color: #c0c4cc;
  font-size: 12px;
  white-space: nowrap;
  line-height: 28px;
}
.more-tags-dropdown-container {
  flex-shrink: 0; /* 防止“更多”按钮被压缩 */
}
.more-tags-btn {
  font-size: 12px;
}
.more-tags-btn-placeholder-inline {
  width: 1px;
}
.more-tags-dropdown-menu {
  padding: 0;
  max-height: 280px;
  .dropdown-draggable-wrapper {
    max-height: inherit;
    overflow-y: auto;
    padding: 5px;
  }
}
.more-tags-draggable-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
.tag-btn-dropdown-item.el-dropdown-menu__item {
  padding: 5px 10px;
  line-height: normal;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab !important;
  font-size: 12px;
  &:focus,
  &:not(.is-disabled):hover {
    background-color: #f5f7fa;
    color: #606266;
  }
  &.is-selected {
    color: #409eff;
    background-color: #ecf5ff;
  }
  .tag-name-in-dropdown {
    flex-grow: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 8px;
  }
  .tag-close-dropdown {
    font-size: 12px;
    color: #909399;
    cursor: pointer;
    padding: 2px;
    border-radius: 50%;
    &:hover {
      color: #f56c6c;
    }
  }
}
.tag-btn {
  position: relative;
  cursor: grab;
  white-space: nowrap;
  border-radius: 12px;
  font-size: 14px;
  padding: 4px 12px;
  .tag-close {
    display: none;
    position: absolute;
    top: 2px;
    right: 4px;
    font-size: 13px;
    color: #bbb;
    cursor: pointer;
    -webkit-transition: color .2s;
    transition: color .2s;
    z-index: 2;
  }
  &:hover .tag-close {
    display: inline-block;
  }
  .tag-close:hover {
    color: #f56c6c;
  }
}
.ghost-tag {
  opacity: 0.5;
  background: #c8ebfb !important;
  border: 1px dashed #409EFF !important;
  border-radius: 4px;
  &.tag-btn {
    color: transparent !important;
    box-shadow: none !important;
  }
  &.el-dropdown-menu__item {
    color: transparent !important;
    background-color: #c8ebfb !important;
    border: 1px dashed #409EFF !important;
    span {
      display: none !important;
    }
  }
}
.el-dropdown-menu__item {
  user-select: none;
}
</style>