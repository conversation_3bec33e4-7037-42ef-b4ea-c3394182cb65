<template>
    <div class="comprehensive-search-action-bar">
      <div class="action-bar-content">
        <!-- 左侧：总条数 -->
        <div class="total-count">
          共 {{ total }} 条
        </div>
  
        <!-- 中间：分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            :current-page.sync="internalCurrentPage"
            :page-size.sync="internalPageSize"
            :total="total"
            layout="prev, pager, next, jumper"
            @current-change="val => $emit('page-change', val)"
          />
        </div>
  
        <!-- 右侧：排序和每页数量 -->
        <div class="sort-and-size-selector">
          <el-select
            v-model="internalSortType"
            placeholder="排序方式"
            size="small"
            style="width: 140px; margin-right: 10px;"
            @change="val => $emit('sort-change', val)"
          >
            <el-option v-for="item in sortOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select
            v-model="internalPageSize"
            size="small"
            style="width: 110px;"
            @change="val => $emit('size-change', val)"
          >
            <el-option label="10 条/页" :value="10"></el-option>
            <el-option label="20 条/页" :value="20"></el-option>
            <el-option label="50 条/页" :value="50"></el-option>
            <el-option label="100 条/页" :value="100"></el-option>
          </el-select>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ComprehensiveSearchActionBar',
    props: {
      total: {
        type: Number,
        default: 0
      },
      currentPage: {
        type: Number,
        default: 1
      },
      pageSize: {
        type: Number,
        default: 50
      },
      sortType: {
        type: String,
        default: ''
      },
      sortOptions: {
        type: Array,
        default: () => []
      }
    },
    computed: {
      internalCurrentPage: {
        get() { return this.currentPage; },
        set(val) { this.$emit('update:currentPage', val); }
      },
      internalPageSize: {
        get() { return this.pageSize; },
        set(val) { this.$emit('update:pageSize', val); }
      },
      internalSortType: {
        get() { return this.sortType; },
        set(val) { this.$emit('update:sortType', val); }
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .comprehensive-search-action-bar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 0 20px;
    height: 60px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .action-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1400px; /* 与主内容区宽度保持一致 */
  }
  
  .total-count {
    font-size: 14px;
    color: #606266;
  }
  
  .pagination-container {
    flex-grow: 1;
    display: flex;
    justify-content: center;
  }
  
  .sort-and-size-selector {
    display: flex;
    align-items: center;
  }
  </style>