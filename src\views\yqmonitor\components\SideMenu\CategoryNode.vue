<template>
  <div class="custom-tree-node">
    <div class="click-area" @click="handleCategoryClick" />
    <div class="node-content">
      <svg-icon icon-class="folder" class-name="custom-class" />
      <span v-if="!isEditing" class="node-label" :title="node.label" @click="handleCategoryClick">{{ node.label
      }}</span>
      <input
        v-else
        ref="input"
        v-model="editingName"
        class="node-input"
        @blur="handleBlur"
        @keyup.enter="handleEnter"
      >
    </div>
    <el-dropdown trigger="click" @command="handleCommand">
      <span class="el-dropdown-link" @click="handleMoreClick">
        <i class="el-icon-more" />
      </span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="addTopic">添加专题</el-dropdown-item>
        <el-dropdown-item command="edit">编辑</el-dropdown-item>
        <el-dropdown-item command="delete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { checkPermi } from '@/utils/permission'
import { addMenu, updateMenu, delMenu } from '@/api/system/menu'

export default {
  name: 'CategoryNode',
  props: {
    node: {
      type: Object,
      required: true
    },
    data: {
      type: Object,
      required: true
    },
    isTreeCurrentlyDragging: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEditing: false,
      editingName: '',
      isEnterPressed: false
    }
  },
  computed: {
    currentNode() {
      return this.$store.state.yqmonitorMenu.currentNode
    },
    currentBusinessType() {
      return this.$store.state.yqmonitorMenu.currentBusinessType
    }
  },
  watch: {
    currentNode(newVal) {
      if (newVal &&
                newVal.id === this.data.id &&
                newVal.type === 'category' &&
                !newVal.isAddingTopic &&
                newVal.isEditing) {
        this.startEdit()
      }
    }
  },
  methods: {
    handleMoreClick(event) {
      if (this.isTreeCurrentlyDragging) {
        event.stopPropagation()
        return
      }
      if (this.node.expanded) {
        event.stopPropagation()
        return
      }
    },
    handleCommand(command) {
      if (this.isTreeCurrentlyDragging) return
      if (command === 'edit' && !checkPermi(['system:menu:edit'])) {
        this.$message.warning('您没有编辑权限')
        return
      }
      if (command === 'delete' && !checkPermi(['system:menu:delete'])) {
        this.$message.warning('您没有删除权限')
        return
      }
      if (command === 'addTopic' && !checkPermi(['system:menu:add'])) {
        this.$message.warning('您没有添加专题的权限')
        return
      }
      if (command === 'edit') {
        this.$store.dispatch('yqmonitorMenu/setCurrentNode', {
          ...this.data,
          isEditing: true
        })
        this.startEdit()
      } else if (command === 'delete') {
        this.deleteCategory()
      } else {
        this.$emit('command', {
          command,
          data: this.data
        })
      }
    },
    deleteCategory() {
      if (this.data.children && this.data.children.length > 0) {
        this.$message.warning('当前分类下存在专题，无法删除')
        return
      }

      this.$confirm('确认删除该分类吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delMenu(this.data.id).then(async(response) => {
          if (response.code === 200) {
            await this.$store.dispatch('yqmonitorMenu/fetchMenuList')
            const menuList = this.$store.state.yqmonitorMenu.menuList
            if (menuList && menuList.length > 0) {
              this.$store.dispatch('yqmonitorMenu/setCurrentNode', menuList[0])
            }
            this.$message.success('删除成功')
          } else {
            this.$message.error('删除失败')
          }
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败')
        })
      }).catch(() => { })
    },
    startEdit() {
      this.isEditing = true
      this.editingName = this.node.label
      this.$nextTick(() => {
        this.$refs.input.focus()
      })
    },
    handleBlur() {
      if (!this.isEnterPressed) {
        this.handleSave()
      }
      this.isEnterPressed = false
    },
    handleEnter() {
      this.isEnterPressed = true
      this.handleSave()
    },
    handleSave() {
      if (this.editingName.trim() !== '') {
        this.updateNodeName(this.editingName)
      }
      this.isEditing = false
    },
    updateNodeName(newName) {
      this.data.name = newName
      // 构造菜单数据
      const menuData = {
        menuName: newName,
        parentId: this.data.parentId,
        orderNum: 1,
        path: this.currentBusinessType,
        menuType: 'M',
        status: '0',
        visible: '0',
        isFrame: '1',
        isCache: '0'
      }

      // 判断是新增还是编辑
      if (typeof this.data.id === 'string' && this.data.id.startsWith('category_')) {
        // 新增分类
        addMenu(menuData).then(response => {
          console.log(response)
          if (response.code === 200) {
            this.$message.success('添加成功')
            // 重新获取菜单列表
            this.$store.dispatch('yqmonitorMenu/fetchMenuList')
          } else {
            this.$message.error('添加失败')
          }
        }).catch(error => {
          console.error('添加失败:', error)
          this.$message.error('添加失败')
        })
      } else {
        // 编辑分类
        menuData.menuId = this.data.id
        updateMenu(menuData).then(response => {
          console.log(response)
          if (response.code === 200) {
            this.$message.success('更新分类成功')
          } else {
            this.$message.error('更新分类失败')
          }
        }).catch(error => {
          console.error('更新分类失败:', error)
          this.$message.error('更新分类失败')
        })
      }
    },
    handleCategoryClick(e) {
      if (this.isTreeCurrentlyDragging) {
        e && e.stopPropagation()
        return
      }
      if (this.node.expanded) {
        console.log('已经展开了不允许关闭')
        e && e.stopPropagation()
      }
      this.$emit('category-click', this.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 8px;
    position: relative;

    .node-content {
        display: flex;
        align-items: center;
    }

    .node-label {
        margin-left: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 130px;
    }

    .node-input {
        margin-left: 8px;
        width: 130px;
        background: transparent;
        border: none;
        border-bottom: 1px solid #409EFF;
        color: #fff;
        outline: none;
        padding: 2px 4px;
        font-size: 16px;
    }
}

.custom-class {
    font-size: 15px;
}

.el-dropdown-link {
    cursor: pointer;
    color: #fff;

    &:hover {
        color: #409EFF;
    }
}

.click-area {
    position: absolute;
    top: -9px;
    right: 30px;
    width: 132px;
    height: 50px;
    cursor: pointer;
}
</style>
