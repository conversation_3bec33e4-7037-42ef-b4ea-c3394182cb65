import request from '@/utils/request'

export function getPublicSentimentList(query) {
  let url = '/yqms/monitor/getPublicSentimentList' // 默认普通列表接口
  const paramsForApi = { ...query }
  if (query.enableSimilarityDedup === 'fold') {
    url = '/yqms/monitor/getFoldedPublicSentimentList' // 调用折叠列表接口
    // query.enableSimilarityDedup === 'fold'时只是用来判断调用哪个接口，所以需要删除
    delete paramsForApi.enableSimilarityDedup
  }

  return request({
    url,
    method: 'post',
    data: paramsForApi
  })
}

export function updateSimilarGroupReadStatusApi(data) {
  return request({
    url: '/yqms/monitor/updateSimilarGroupReadStatus',
    method: 'post',
    data: data
  })
}

/**
 * 获取指定相似组内的所有相似舆情数据
 * @param {object} data - 请求体
 * @param {string} data.similarGroupId - 相似组ID
 * @param {string} data.uniqueId - 舆情唯一ID
 */
export function getSimilarGroupListApi(data) {
  return request({
    url: '/yqms/monitor/getSimilarGroupList',
    method: 'post',
    data: data
  })
}

/**
 * 查询过滤词列表
 * @param {Object} query
 * @param {number} query.userId - 用户ID
 * @param {number} query.menuId - 菜单ID
 * @returns {Promise} 返回过滤词列表
 */
export function getFilterWordList(query) {
  return request({
    url: '/system/param/list',
    method: 'get',
    params: query
  })
}

/**
 * 新增过滤词
 * @param {Object} data
 * @param {number} data.userId - 用户ID
 * @param {string} data.name - 名称
 * @param {number} data.menuId - 菜单ID
 * @param {string} data.paramJson - 筛选词json
 * @returns {Promise} 返回操作结果
 */
export function addFilterWord(data) {
  return request({
    url: '/system/param/add',
    method: 'post',
    data: data
  })
}

/**
 * 修改过滤词
 * @param {Object} data
 * @param {number} data.userId - 用户ID
 * @param {string} data.name - 名称
 * @param {number} data.menuId - 菜单ID
 * @param {string} data.paramJson - 筛选词json
 * @returns {Promise} 返回操作结果
 */
export function updateFilterWord(data) {
  return request({
    url: '/system/param/edit',
    method: 'post',
    data: data
  })
}

/**
 * 删除过滤词
 * @param {Object} data
 * @param {number} data.userId - 用户ID
 * @param {string} data.name - 名称
 * @param {number} data.menuId - 菜单ID
 * @returns {Promise} 返回操作结果
 */
export function deleteFilterWord(data) {
  return request({
    url: '/system/param/delete',
    method: 'post',
    data: data
  })
}

/**
 * 批量更新舆情已读状态
 * @param {Object} data
 * @param {Array} data.uniqueIds - 舆情唯一ID数组
 * @param {number} data.readStatus - 已读状态（1为已读）
 * @returns {Promise}
 */
export function batchUpdatePublicSentiment(data) {
  return request({
    url: '/yqms/monitor/batchUpdatePublicSentiment',
    method: 'post',
    data
  })
}

/**
 * 批量修改舆情倾向性
 * @param {Object} data
 * @param {Array} data.uniqueIds - 舆情唯一ID数组
 * @param {number} data.sensitivity - 倾向性（如3）
 * @returns {Promise}
 */
export function batchUpdateSentiment(data) {
  return request({
    url: '/yqms/monitor/batchUpdatePublicSentiment',
    method: 'post',
    data
  })
}

/**
 * 批量修改AI舆情倾向性
 * @param {Object} data
 * @param {Array} data.uniqueIds - 舆情唯一ID数组
 * @param {number} data.aiSentimentType - AI舆情类型
 * @returns {Promise}
 */
export function batchUpdateAiSentimentType(data) {
  return request({
    url: '/yqms/monitor/batchUpdatePublicSentiment',
    method: 'post',
    data
  })
}

/**
 * 批量删除舆情
 * @param {Object} data
 * @param {Array} data.uniqueIds - 舆情唯一ID数组
 * @returns {Promise}
 */
export function batchDeletePublicSentiment(data) {
  return request({
    url: '/yqms/monitor/batchDeletePublicSentiment',
    method: 'post',
    data
  })
}

/**
 * 查询大于等于指定时间点的舆情数据个数
 * @param {Object} data
 * @param {string} data.pushTime - 查询时间点
 * @returns {Promise<{total: number}>}
 */
export function getPublicSentimentCount(data) {
  const paramsForApi = { ...data }
  if (data.enableSimilarityDedup === 'fold') {
    delete paramsForApi.enableSimilarityDedup
  }
  return request({
    url: '/yqms/monitor/getPublicSentimentCount',
    method: 'post',
    data: paramsForApi
  })
}

/**
 * 获取舆情详情
 * @param {string} id - 舆情ID
 * @returns {Promise} 返回舆情详情数据
 */
export function getPublicSentimentById(id) {
  return request({
    url: `/yqms/monitor/getPublicSentimentById/${id}`,
    method: 'get'
  })
}
