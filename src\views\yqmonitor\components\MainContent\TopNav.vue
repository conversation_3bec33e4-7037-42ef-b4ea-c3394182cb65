<template>
  <div class="top-nav-vertical">
    <!-- 上部分：left-box + 菜单栏 + 右侧按钮 -->
    <div class="top-row">
      <div v-if="currentNode" class="left-box">
        <span class="title">{{ currentNodeName }}</span>
        <el-button v-if="currentNode.planId" type="text" icon="el-icon-setting" class="setting-btn" size="small"
          @click="handleSettingClick" />
      </div>
      <div class="center-box">
        <div v-show="!showSearchInput" class="center-search-container">
          <el-menu mode="horizontal" :default-active="activeNav" class="center-menu" @select="handleNavSelect">
            <el-menu-item v-for="item in mediaTypeMenu" :key="item.value" :index="item.value" style="font-size: 16px;">
              {{ item.label }}
            </el-menu-item>
          </el-menu>
          <i class="el-icon-search search-icon" @click="handleSearchIconClick" />
        </div>
        <div v-show="showSearchInput" class="search-bar">
          <el-select v-model="searchFields" multiple placeholder="" @change="handleSearchFieldsChange">
            <el-option v-for="item in searchFieldOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-input v-model="searchText" placeholder="请输入搜索内容，多个词请用空格分隔" style="width: 300px; margin-left: 8px;"
            class="search-input" @keyup.enter.native="handleSearch" />
          <i class="el-icon-search search-icon" @click="handleSearch" />
        </div>
      </div>
      <div v-show="showSearchInput" class="toprow_right-group">
        <el-button size="small" class="exit-search-btn" @click="handleExitSearchBar">
          退出
        </el-button>
      </div>
    </div>
    <!-- 下部分修改：左侧标签区 + 右侧操作区 -->
    <div class="tags-and-actions-row">
      <!-- 左容器: 标签管理器 -->
      <FilterTagManager ref="filterTagManagerRef" :filter-tags="filterTags" :max-visible-tags="maxVisibleTags"
        class="tags-container-left" @tag-selected="handleTagManagerTagSelected"
        @tags-config-changed="handleTagManagerTagsConfigChanged"
        @tags-updated-by-delete="handleTagsUpdatedByDeleteInManager" />
      <!-- 右容器: 筛选/刷新/新增提醒 -->
      <div class="right-controls-group">
        <div class="top-buttons">
          <el-button size="small" icon="el-icon-search" @click="handleOpenFilterDialog">筛选</el-button>
          <el-dropdown trigger="click" @command="handleRefreshModeChange">
            <el-button size="small" icon="el-icon-refresh">
              {{ refreshMode === 'manual' ? '手动刷新' : '自动刷新' }}
              <i class="el-icon-arrow-down el-icon--right" />
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="manual">手动刷新</el-dropdown-item>
              <el-dropdown-item command="auto">自动刷新</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <div v-if="showNewInfoTip && refreshMode === 'manual'" class="new-info-tip" @click="handleGetNewInfo">
          <span>有新增消息，</span>
          <span class="get-btn">点击获取</span>
          <i class="el-icon-refresh" style="cursor:pointer; margin-left: 4px;" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import FilterTagManager from './FilterTagManager.vue'
import { getPublicSentimentCount } from '@/api/yqmonitor'

// 开发环境为了防止轮询的频率太高影响开发调试，设置为24小时 24 * 60 * 60 * 1000
const REFRESH_INTERVAL = process.env.NODE_ENV === 'development' ? 24 * 60 * 60 * 1000 : 10000

export default {
  name: 'TopNav',
  dicts: ['media_type', 'search_field'],
  components: {
    FilterTagManager
  },
  props: {
    filterTags: {
      type: Array,
      default: () => []
    },
    maxVisibleTags: {
      type: Number,
      required: true
    }
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['currentNode', 'lastQueryParams']),
    currentNodeName() {
      return this.currentNode?.name || ''
    },
    mediaTypeMenu() {
      const dict = this.dict.type.media_type || []
      const hasAll = dict.some(item => item.value === 'all')
      if (hasAll) return dict
      return [{ label: '全部', value: 'all' }, ...dict]
    },
    searchFieldOptions() {
      return this.dict.type.search_field || []
    }
  },
  data() {
    return {
      activeNav: 'all',
      refreshMode: 'manual', // manual/auto
      refreshTimer: null,
      showNewInfoTip: false,
      lastPushTime: '',
      showSearchInput: false,
      searchFields: ['all'],
      searchText: ''
    }
  },
  watch: {
    refreshMode() {
      this.clearRefreshTimer()
      this.startAutoRefresh()
    }
  },
  mounted() {
    this.lastPushTime = this.getNowTimeString()
    this.startAutoRefresh()
    this.$el.addEventListener('click', this.handleTopNavClick)
  },
  beforeDestroy() {
    this.clearRefreshTimer()
    this.$el.removeEventListener('click', this.handleTopNavClick)
  },
  methods: {
    handleSearchIconClick() {
      this.showSearchInput = !this.showSearchInput
    },
    handleRefreshModeChange(mode) {
      this.refreshMode = mode
    },
    handleTagManagerTagsUpdated() {
      this.$emit('refreshTags')
    },
    startAutoRefresh() {
      this.clearRefreshTimer()
      this.showNewInfoTip = false
      this.refreshTimer = setInterval(this.loopTask, REFRESH_INTERVAL)
    },
    loopTask() {
      if (this.refreshMode === 'manual') {
        this.checkNewInfo()
      } else {
        this.handleRefresh()
      }
    },
    clearRefreshTimer() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }
    },
    async checkNewInfo() {
      if (!this.lastPushTime) return
      try {
        const params = { ...this.lastQueryParams, pushTime: this.lastPushTime }
        const res = await getPublicSentimentCount(params)
        if (res && res.data > 0) {
          this.showNewInfoTip = true
          this.clearRefreshTimer()
        } else {
          this.showNewInfoTip = false
        }
      } catch (e) {
        // 错误处理
      }
    },
    handleGetNewInfo() {
      this.showNewInfoTip = false
      this.lastPushTime = this.getNowTimeString()
      this.$emit('refresh')
      this.startAutoRefresh()
    },
    getNowTimeString() {
      const pad = n => n < 10 ? '0' + n : n
      const d = new Date()
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`
    },
    handleNavSelect(key) {
      this.activeNav = key
      this.$emit('nav-change', { mediaTypes: key === 'all' ? [] : [key] })
    },
    resetActiveNav() {
      this.activeNav = 'all'
    },
    handleTagManagerOpenFilterDrawer(selectedTagInfo) {
      this.$emit('filterOpen', selectedTagInfo)
    },
    handleOpenFilterDialog() {
      const selectedTagId = this.$refs.filterTagManagerRef.getSelectedTagId()
      if (selectedTagId) {
        const selectedTag = this.filterTags.find(tag => tag.id === selectedTagId)
        this.$emit('filterOpen', selectedTag ? { editingTagId: selectedTag.id, paramJson: selectedTag.paramJson } : null)
      } else {
        this.$emit('filterOpen', null)
      }
    },
    handleRefresh() {
      this.$emit('refresh')
    },
    handleSettingClick() {
      if (this.currentNode) {
        this.$store.commit('yqmonitorTopic/SET_TOPIC_DRAWER_VISIBLE', true)
      }
    },
    handleTagManagerTagSelected(tag) {
      this.$emit('tagClick', tag)
    },
    resetSelectedTag() {
      if (this.$refs.filterTagManagerRef) {
        this.$refs.filterTagManagerRef.resetSelectedTagState()
      }
    },
    setSelectedTag(tagId) {
      if (this.$refs.filterTagManagerRef) {
        this.$refs.filterTagManagerRef.setSelectedTagState(tagId)
      }
    },
    handleTopNavClick(e) {
      if (e.target.closest('.tag-btn')) return
      if (
        e.target.closest('.right-action-buttons') ||
        e.target.closest('.el-dropdown') ||
        e.target.closest('.center-search-container') ||
        e.target.closest('.search-bar') ||
        e.target.closest('.new-info-tip') ||
        e.target.closest('.exit-search-btn')
      ) {
        return
      }
      const childSelectedTagId = this.$refs.filterTagManagerRef?.getSelectedTagId()
      if (childSelectedTagId) {
        console.log('2. [TopNav] A tag is selected, I am resetting it now!'); // <--- 添加这行
        this.$emit('resetFilter', true)
      }
    },
    handleSearch() {
      const fields = this.searchFields.includes('all') || this.searchFields.length === 0 ? [] : this.searchFields
      this.$emit('search', { fields, text: this.searchText })
    },
    handleSearchFieldsChange(val) {
      const allValue = 'all'
      if (val.includes(allValue) && val.length > 1) {
        if (val[val.length - 1] === allValue) {
          this.searchFields = [allValue]
        } else {
          this.searchFields = val.filter(item => item !== allValue)
        }
      }
    },
    handleExitSearchBar(e) {
      this.showSearchInput = false
      this.searchFields = ['all']
      this.searchText = ''
      // 如果是父组件调用当前方法则不冒泡事件
      if (e === 'no-emit') return
      this.$emit('clear-search')
    },
    handleTagManagerTagsConfigChanged(newOrderedTags) {
      this.$emit('tags-config-changed', newOrderedTags)
    },
    handleTagsUpdatedByDeleteInManager() {
      this.$emit('refreshTags')
    }
  }
}
</script>

<style lang="scss" scoped>
.top-nav-vertical {
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  padding: 0 10px;

  .top-row {
    display: flex;
    align-items: center;
    height: 40px;
  }

  .left-box {
    display: flex;
    align-items: center;
    width: 33.333%;

    .title {
      font-weight: bold;
      font-size: 18px;
      margin-right: 4px;
      display: block;
      max-width: 200px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }

    .setting-btn {
      font-size: 18px;
    }
  }

  .center-box {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    width: 33.333%;
  }

  .toprow_right-group {
    width: 33.333%;
    display: flex;
    justify-content: flex-end;
  }

  .center-search-container {
    display: flex;
    align-items: center;

    .search-icon {
      font-size: 20px;
      margin-left: 10px;
      cursor: pointer;
      color: #1976d2;
    }
  }

  .search-bar {
    display: flex;
    align-items: center;
    border-bottom: 1px solid #1976d2;

    .el-select,
    .el-input {
      height: 38px;
      line-height: 38px;
      border-radius: 0;
      box-shadow: none;
    }

    .el-select {
      width: 100px;

      ::v-deep .el-select__tags {
        display: none;
      }

      ::v-deep>.el-input {
        .el-input__inner {
          outline: none;
          border: none;
        }

        &::after {
          content: "匹配(...)";
          color: #333;
          font-size: 14px;
          font-weight: 500;
          position: absolute;
          right: 39px;
        }
      }
    }

    .el-input {
      ::v-deep .el-input__inner {
        border: none;
        outline: none;
      }
    }

    .search-icon {
      font-size: 20px;
      margin-left: 8px;
      cursor: pointer;
      color: #1976d2;
      transition: color 0.2s;
    }

    .search-icon:hover {
      color: #1251a3;
    }
  }

  .center-menu {
    flex: 1;
    display: flex;
    justify-content: center;
    background: transparent;
    border-bottom: none;
    height: 40px;
    line-height: 40px;

    .el-menu-item {
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      padding: 0 12px;
    }
  }

  .new-info-tip {
    margin-top: 1px;
    text-align: center;
    color: #1976d2;
    font-size: 14px;
    border: 1px solid #1976d2;
    border-radius: 4px;
    display: inline-block;
    cursor: pointer;
    transition: all 0.3s;
    background: transparent;
    width: 100%;

    &:hover {
      background: rgba(25, 118, 210, 0.1);
    }

    .el-button {
      margin-left: 8px;
      color: #1976d2;
      font-weight: 500;
      padding-top: 8px;
      padding-bottom: 8px;
    }

    i {
      margin-left: 8px;
      color: #1976d2;
    }
  }

  .exit-search-btn {
    margin-left: auto;
  }
}

.tags-and-actions-row {
  display: flex;
  gap: 16px;
  /* 左右容器间距 */
  align-items: flex-start;
  /* 顶部对齐 */
  margin-top: 8px;
}

.tags-container-left {
  flex-grow: 1;
  /* 占据剩余空间 */
  min-width: 0;
}

.right-controls-group {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  /* 内容靠右对齐 */
  gap: 8px;
  /* 按钮组和提示信息的垂直间距 */
  flex-shrink: 0;
}

.top-buttons {
  display: flex;
  gap: 8px;
}

.new-info-tip {
  padding: 6px 12px;
  color: #1976d2;
  font-size: 12px;
  border: 1px solid #1976d2;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  cursor: pointer;
  transition: all 0.3s;
  background: transparent;

  &:hover {
    background: rgba(25, 118, 210, 0.1);
  }

  .get-btn {
    margin-left: 4px;
    text-decoration: underline;
    color: #1976d2;
    font-weight: 500;
  }

  i {
    color: #1976d2;
  }
}

.exit-search-btn {
  margin-left: auto;
}
</style>
