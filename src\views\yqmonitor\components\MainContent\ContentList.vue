<template>
  <div class="content-list">
    <div v-if="mappedList.length === 0" class="empty-state">
      <el-empty description="暂无数据" />
    </div>
    <div v-for="(item, index) in mappedList" :key="item.frontId" class="list-item">
      <Article :item="item" :index="index" :checked-visible="checkedVisible"
        :checked="checkedList.includes(item.uniqueId)" :sentiment-options="sentimentOptions"
        :ai-sentiment-options="aiSentimentOptions"
        @check-change="val => handleCheckChange(item.uniqueId, val)" @delete="handleDelete"
        @change-sentiment="val => handleChangeSentiment(item.uniqueId, val)"
        @change-ai-sentiment="val => handleChangeAiSentiment(item.uniqueId, val)" @mark-read="handleMarkRead"
        @expand-similar="payload => handleArticleExpandSimilar(payload)" />
    </div>
    <div v-if="total > 1" class="list-pagination">
      <el-pagination background layout="sizes, prev, pager, next, jumper" :current-page="pageNum" :page-size="pageSize"
        :page-sizes="[30, 50, 100]" :total="total" @current-change="onPageChange" @size-change="onSizeChange" />
      <span class="total-text">共{{ total }}条</span>
    </div>
  </div>
</template>

<script>
import Article from './Article.vue'
import { mapItem } from './articleUtil'

export default {
  name: 'ContentList',
  components: { Article },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    pageNum: {
      type: Number,
      default: 1
    },
    pageSize: {
      type: Number,
      default: 30
    },
    total: {
      type: Number,
      default: 0
    },
    checkedList: {
      type: Array,
      default: () => []
    },
    sentimentOptions: {
      type: Array,
      default: () => []
    },
    aiSentimentOptions: {
      type: Array,
      default: () => []
    },
    checkedVisible: {
      type: Boolean,
      default: true
    },
  },
  computed: {
    startIndex() {
      return (this.pageNum - 1) * this.pageSize
    },
    mappedList() {
      return this.list.map(item => mapItem(item))
    },
  },
  methods: {
    onPageChange(page) {
      this.$emit('page-change', page)
    },
    onSizeChange(size) {
      this.$emit('size-change', size)
    },
    handleCheckChange(id, checked) {
      this.$emit('check-change', { id, checked })
    },
    handleDelete(item) {
      this.$emit('delete', item)
    },
    handleChangeSentiment(id, value) {
      this.$emit('change-sentiment', { id, value })
    },
    handleChangeAiSentiment(id, value) {
      this.$emit('change-ai-sentiment', { id, value })
    },
    handleMarkRead(uniqueId) {
      this.$emit('mark-read', uniqueId)
    },
    handleArticleExpandSimilar(payload) {
      this.$emit('expand-similar', payload)
    }
  }
}
</script>

<style lang="scss" scoped>
.content-list {
  position: relative;
  min-height: calc(100vh - 200px);
  padding-bottom: 120px;

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
  }

  .list-item {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;

    .item-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .item-index {
        margin-right: 10px;
      }

      .el-tag+.el-tag {
        margin-left: 5px;
      }

      .item-title {
        margin-left: 10px;
        flex: 1;

        &.is-read {
          color: #909399;
        }
      }

      .item-stats {
        margin-left: 15px;

        span {
          margin-left: 15px;
          color: #909399;
          font-size: 12px;

          i {
            margin-right: 3px;
          }
        }
      }
    }

    .item-content {
      margin-bottom: 10px;

      .content-text {
        margin-bottom: 10px;
      }

      .content-images {
        display: flex;
        gap: 10px;
        margin-top: 10px;

        .el-image {
          width: 200px;
          height: 150px;
          border-radius: 4px;
          overflow: hidden;

          .image-slot {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: #f5f7fa;
            color: #909399;
            font-size: 20px;
          }
        }
      }
    }

    .item-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #909399;
      font-size: 12px;

      .info-group {
        display: flex;
        align-items: center;
        gap: 15px;

        .el-rate {
          display: inline-flex;
          height: 20px;
        }
      }
    }
  }

  .list-pagination {
    position: fixed;
    bottom: 60px;
    left: 220px;
    width: calc(100vw - 200px - 40px);
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 0;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    z-index: 9;

    .total-text {
      margin-left: 16px;
      color: #909399;
      font-size: 14px;
    }
  }
}
</style>
