<template>
  <div class="article-container">
    <div class="item-header">
      <el-checkbox
        v-if="checkedVisible"
        class="article-checkbox"
        :value="checked"
        style="margin-right: 8px"
        @change="(val) => $emit('check-change', val)"
      />
      <span class="item-index">{{ index + 1 }}</span>
      <span v-if="item.moduleReadStatus === 1" class="read-status">已读</span>
      <el-tag size="small" :type="aiPublicOpinionTypeColor" effect="dark">{{
        aiPublicOpinionType
      }}</el-tag>
      <el-tag size="small" :type="articleTypeColor" effect="plain">{{
        articleType
      }}</el-tag>
      <el-tooltip v-if="item.titleObj.rawTitle" placement="top" :open-delay="contentTooltipDelay">
        <template #content>
          <div v-html="highlightHitWords(item.titleObj.rawTitle)" />
        </template>
        <span
          class="item-title"
          :class="{ 'is-read': item.moduleReadStatus === 1 }"
          @click="handleTitleClick(item)"
          v-html="highlightHitWords(item.titleObj.title, false)"
        />
      </el-tooltip>
      <span
        v-else
        class="item-title"
        :class="{ 'is-read': item.moduleReadStatus === 1 }"
        @click="handleTitleClick(item)"
        v-html="highlightHitWords(item.titleObj.title, false)"
      />

      <div class="item-stats">
        <el-tooltip
          v-if="item.similarCount && item.similarCount > 0"
          content="相似舆情"
          placement="top"
          :open-delay="iconTooltipDelay"
        >
          <span class="similar-count-badge" @click.stop="handleExpandSimilar">
            <svg-icon icon-class="similar" class-name="similar-icon" />
            {{ item.similarCount }}
          </span>
        </el-tooltip>
        <el-tooltip content="浏览量" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-view" /> {{ item.views }}</span>
        </el-tooltip>
        <el-tooltip content="评论数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-chat-line-square" /> {{ item.comments }}</span>
        </el-tooltip>
        <el-tooltip content="点赞数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-star-off" /> {{ item.likes }}</span>
        </el-tooltip>
        <el-tooltip content="转发数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-share" /> {{ item.reposts }}</span>
        </el-tooltip>
        <el-tooltip content="收藏数" placement="top" :open-delay="iconTooltipDelay">
          <span><i class="el-icon-collection" /> {{ item.collects }}</span>
        </el-tooltip>
      </div>
    </div>

    <div class="item-content">
      <!-- 原创内容 -->
      <div v-if="item.isOriginal === 1" class="content-text">
        <!-- 内容 -->
        <template v-if="item.contentObj.content">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawContent)" />
            <span slot="reference" v-html="highlightHitWords(item.contentObj.content, false)" />
          </el-popover>
        </template>

        <!-- ocr -->
        <div v-if="item.contentObj.ocr" class="ocr-block">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawOcr)" />
            <span slot="reference" class="ocr-content" v-html="highlightHitWords(item.contentObj.ocr, false)" />
          </el-popover>
        </div>

        <!-- asr -->
        <div v-if="item.contentObj.asr" class="asr-block">
          <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
            <div v-html="highlightHitWords(item.contentObj.rawAsr)" />
            <span slot="reference" class="asr-content" v-html="highlightHitWords(item.contentObj.asr, false)" />
          </el-popover>
        </div>
      </div>
      <!-- 非原创内容 -->
      <div v-else class="content-text">
        <template>
          <div class="origin-inline">
            <!-- 内容 -->
            <template v-if="item.contentObj.content">
              <el-popover placement="top" trigger="click" :width="400" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawContent)" />
                <span
                  slot="reference"
                  class="origin-main-content"
                  v-html="highlightHitWords(item.contentObj.content, false)"
                />
              </el-popover>
            </template>
            <!-- ocr -->
            <div v-if="item.contentObj.ocr" class="ocr-block">
              <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawOcr)" />
                <span
                  slot="reference"
                  class="ocr-content"
                  v-html="highlightHitWords(item.contentObj.ocr, false)"
                />
              </el-popover>
            </div>

            <!-- asr -->
            <div v-if="item.contentObj.asr" class="asr-block">
              <el-popover placement="top" trigger="click" :width="800" popper-class="content-popover">
                <div v-html="highlightHitWords(item.contentObj.rawAsr)" />
                <span
                  slot="reference"
                  class="asr-content"
                  v-html="highlightHitWords(item.contentObj.asr, false)"
                />
              </el-popover>
            </div>
          </div>
        </template>
      </div>
    </div>

    <div class="item-footer">
      <div class="info-group">
        <span class="platform" @click="handleJumpSource"><el-tag
          size="mini"
          type="info"
          :class="{ 'hitword-highlight-specail': mediaHitWords.length > 0, 'is-key-item': mediaHitWords.length > 0 }"
        >{{ item.source }}</el-tag></span>
        <span class="author-info" :class="{'is-key-item': accountHitWords.length > 0 }">
          <el-image
            v-if="item.avatar"
            :src="item.avatar"
            style="
              width: 20px;
              height: 20px;
              border-radius: 50%;
              margin-right: 4px;
            "
            fit="cover"
          />
          <a
            v-if="item.authorUrl"
            :href="item.authorUrl"
            target="_blank"
            :class="{ 'hitword-highlight-specail': accountHitWords.length > 0 }"
            class="author-link"
          >{{ item.author
          }}</a>
          <span v-else :class="{ 'hitword-highlight-specail': accountHitWords.length > 0 }">{{ item.author }}</span>
        </span>
        <span class="fans">粉丝：{{ item.fansCount }}</span>
        <span v-if="item.location" class="location">地域：{{ item.location }}</span>
        <span class="sentiment-tag">万有引力倾向性：<el-tag size="mini" :type="sentimentTypeColor">{{
          sentiment
        }}</el-tag></span>
      </div>
      <div class="item-icons">
        <el-button v-hasPermi="['yqmonitor:createWarning','followedEvents:createWarning','targetMonitor:createWarning']" class="warning-btn" size="mini" type="text" @click="handleWarning">
          预警
        </el-button>
        <el-tooltip content="跳转源站" placement="top">
          <i class="el-icon-link" @click="handleJumpSource" />
        </el-tooltip>
        <el-tooltip content="复制url" placement="top">
          <i class="el-icon-document-copy" @click="handleCopyUrl" />
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:editSentiment','followedEvents:editSentiment','targetMonitor:editSentiment']" content="修改倾向性" placement="top">
          <el-dropdown trigger="click" @command="(val) => handleEditSentiment(val)">
            <span class="el-dropdown-link">
              <i class="el-icon-edit" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in sentimentOptions.filter(
                  (item) => item.value !== 'all'
                )"
                :key="item.value"
                :command="item.value"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:editAiSentiment','followedEvents:editAiSentiment','targetMonitor:editAiSentiment']" content="修改AI舆情倾向性" placement="top">
          <el-dropdown trigger="click" @command="(val) => handleEditAiSentiment(val)">
            <span class="el-dropdown-link">
              <i class="el-icon-document-add" />
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                v-for="item in aiSentimentOptions.filter(
                  (item) => item.value !== 'all'
                )"
                :key="item.value"
                :command="item.value"
              >
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-tooltip>
        <el-tooltip content="复制" placement="top">
          <i class="el-icon-files" @click="handleCopy" />
        </el-tooltip>
        <el-tooltip v-hasPermi="['yqmonitor:delete','followedEvents:delete','targetMonitor:delete']" content="删除" placement="top">
          <i class="el-icon-delete" @click="handleDelete" />
        </el-tooltip>
      </div>
    </div>
    <div class="item-footer_last">
      <div class="hit-word_wrap">
        <!-- AI舆情特征详情（已自动排除特定类型）-->
        <div v-if="aiFeatureHitWords.length" class="ai-feature-details">
          <div v-for="feature in aiFeatureHitWords" :key="feature.key" class="feature-item">
            <span class="feature-reason">{{ feature.label }}:</span>
            <span class="feature-hitword">{{ feature.value.join(", ") }}</span>
          </div>
        </div>
        <span v-if="wyylHitWords.length" class="hitword">
          <span class="hitword-label">命中词：</span>
          <el-tooltip v-if="wyylHitWords.join(',').length > 8" :content="wyylHitWords.join(',')" placement="top">
            <span class="hitword-content">{{
              wyylHitWords.join(",").slice(0, 8) + "..."
            }}</span>
          </el-tooltip>
          <span v-else class="hitword-content">{{
            wyylHitWords.join(",")
          }}</span>
        </span>
      </div>
      <div class="time-wrap">
        <el-popover
          v-if="item.time !== item.firstCollectionTime"
          placement="top"
          trigger="click"
          :content="'首次采集时间：' + item.firstCollectionTime"
          effect="light"
        >
          <span
            slot="reference"
            class="time collect-time-highlight"
            style="cursor: pointer;"
          >
            采集时间：{{ item.time }}
          </span>
        </el-popover>
        <span
          v-else
          class="time"
        >
          采集时间：{{ item.time }}
        </span>
        <span class="publish-time">发布时间：{{ item.publishTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import SvgIcon from '@/components/SvgIcon'
import {
  getWyylHitWords,
  getAiFeatureHitWords,
  getInstitutionAliasHitWords,
  getMediaHitWords,
  getAccountHitWords,
  getExcludedFromHighlight
} from './articleUtil'
import { mapGetters } from 'vuex'

// const IS_PRODUCTION = process.env.NODE_ENV === "production";

export default {
  name: 'Article',
  components: { SvgIcon },
  dicts: [
    'content_publish_type',
    'ai_public_opinion_type',
    'negative_public_opinion_tag',
    'filter_sentiment'
  ],
  // data() {
  //   return {
  //     isProduction: IS_PRODUCTION,
  //   };
  // },
  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      default: 0
    },
    checked: {
      type: Boolean,
      default: false
    },
    checkedVisible: {
      type: Boolean,
      default: true
    },
    contentTooltipDelay: {
      type: Number,
      default: 1500
    },
    iconTooltipDelay: {
      type: Number,
      default: 300
    },
    sentimentOptions: {
      type: Array,
      default: () => []
    },
    aiSentimentOptions: {
      type: Array,
      default: () => []
    },
  },
  computed: {
    ...mapGetters('yqmonitorMenu', ['searchText']),
    articleType() {
      const type = this.dict.type.content_publish_type.find(
        (item) => String(item.value) === String(this.item.isOriginal)
      )
      return type ? type.label : ''
    },
    articleTypeColor() {
      const colors = {
        原创: 'success',
        转发: 'warning',
        评论: 'info'
      }
      return colors[this.articleType] || 'info'
    },
    aiPublicOpinionType() {
      if (!this.item.dmxTagInfo) return ''
      const type = this.dict.type.ai_public_opinion_type.find(
        (item) => String(item.value) === String(this.item.dmxTagInfo.label)
      )
      return type ? type.label : ''
    },
    aiPublicOpinionTypeColor() {
      return this.aiPublicOpinionType === '敏感' ? 'danger' : ''
    },
    dmxTagInfoReason() {
      if (!this.item.dmxTagInfo) return []
      return this.item.dmxTagInfo.features
        .filter((value) => value.hitWord && value.hitWord.length > 0)
        .map((value) => {
          const reason = this.dict.type.negative_public_opinion_tag.find(
            (item) => String(item.value) === String(value.reason)
          )
          return reason ? reason.label : ''
        })
    },
    sentiment() {
      const type = this.dict.type.filter_sentiment.find(
        (item) => String(item.value) === String(this.item.sentiment)
      )
      return type ? type.label : ''
    },
    sentimentTypeColor() {
      const colors = {
        中性: 'info',
        非敏感: 'success',
        敏感: 'danger'
      }
      return colors[this.sentiment] || 'info'
    },
    isContentEllipsis() {
      return (
        this.item.contentObj.content &&
        this.item.contentObj.content.endsWith('...')
      )
    },
    isOcrEllipsis() {
      return (
        this.item.contentObj.ocr && this.item.contentObj.ocr.endsWith('...')
      )
    },
    isAsrEllipsis() {
      return (
        this.item.contentObj.asr && this.item.contentObj.asr.endsWith('...')
      )
    },
    wyylHitWords() {
      return getWyylHitWords(this.item.hitWordsInfo)
    },
    aiFeatureHitWords() {
      return getAiFeatureHitWords(this.item.hitWordsInfo)
    },
    // 媒体命中体
    mediaHitWords() {
      return getMediaHitWords(this.item.hitWordsInfo)
    },

    // 账号命中词
    accountHitWords() {
      return getAccountHitWords(this.item.hitWordsInfo)
    },

    // 需要排除的高亮词(即：明星、名企、敏感媒体、敏感账号、重点媒体、重点账号 取消旧的高亮词取消旧的高亮逻辑)
    excludedHitWords() {
      return getExcludedFromHighlight(this.item.hitWordsInfo)
    },
  },
  methods: {
    highlightHitWords(text, isReplaceBr = true) {
      // 只从 getWyylHitWords、getAiFeatureHitWords、getInstitutionAliasHitWords 获取高亮词
      let words = []
      // 万有引力命中词
      words = words.concat(getWyylHitWords(this.item.hitWordsInfo))
      // AI舆情特征详情（value为数组）
      const aiFeatureList = getAiFeatureHitWords(this.item.hitWordsInfo)
      aiFeatureList.forEach((feature) => {
        if (Array.isArray(feature.value)) {
          words = words.concat(feature.value)
        }
      })
      // 机构别名命中词
      words = words.concat(getInstitutionAliasHitWords(this.item.hitWordsInfo))
      // 搜索词
      if (this.searchText) {
        // 将搜索词按空格拆分，并过滤掉空字符串
        const searchWords = this.searchText.split(/\s+/).filter((word) => word)
        words.push(...searchWords)
      }
      // 获取敏感词并从高亮列表中排除
      // const sensitiveWords = getSensitiveHitWords(this.item.hitWordsInfo);
      // const sensitiveSet = new Set(sensitiveWords);
      // words = words.filter((word) => !sensitiveSet.has(word));
      // 排除特定类型的命中词
      const excludedWords = this.excludedHitWords
      words = words.filter((word) => !excludedWords.includes(word))
      // 去重、去空
      words = Array.from(new Set(words)).filter(Boolean)
      // 按词长度降序排序，确保长词优先匹配
      words.sort((a, b) => b.length - a.length)
      if (!text || !words.length) return text
      // 构造正则，逐个高亮
      let html = text
      words.forEach((word) => {
        if (!word) return
        const reg = new RegExp(
          word.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'),
          'g'
        )
        html = html.replace(
          reg,
          `<span class="hitword-highlight">${word}</span>`
        )
      })
      if (isReplaceBr) {
        html = html.replace(/\n/g, '<br/>')
      }
      return html
    },
    handleDelete() {
      this.$confirm('确认删除该文章吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('delete', this.item)
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleJumpSource() {
      window.open(this.item.url, '_blank')
    },
    handleCopyUrl() {
      if (this.item.url) {
        navigator.clipboard
          .writeText(this.item.url)
          .then(() => {
            this.$message.success('链接已复制到剪切板')
          })
          .catch(() => {
            this.$message.error('复制失败，请手动复制')
          })
      } else {
        this.$message.warning('没有可复制的链接')
      }
    },
    handleCopy() {
      // 提取摘要(使用全文内容，没有则使用ASR，再没有则使用OCR)
      const summary = this.item.contentObj?.rawContent
        ? this.item.contentObj.rawContent
        : this.item.contentObj?.rawAsr
          ? this.item.contentObj.rawAsr
          : this.item.contentObj?.rawOcr
            ? this.item.contentObj.rawOcr
            : ''

      // 提取作者ID
      const authorId = this.item.authorId || ''

      // 提取认证类型
      const verifyType = this.item.verifyInfo?.verifyType || '普通用户'

      // 提取关键词，优先使用hitWordsInfo中的wyylHitWords
      let keywords = ''

      // 处理命中词
      if (this.hitWordsInfo) {
        // 收集所有命中词到一个数组
        const allHitWords = []

        for (const key in this.hitWordsInfo) {
          if (
            Array.isArray(this.hitWordsInfo[key]) &&
            this.hitWordsInfo[key].length > 0
          ) {
            allHitWords.push(...this.hitWordsInfo[key])
          }
        }

        // 去重（如果需要）
        const uniqueHitWords = [...new Set(allHitWords)]

        // 用中文逗号连接
        if (uniqueHitWords.length > 0) {
          keywords = uniqueHitWords.join('，')
        }
      }

      // 涉事省份，从location提取
      const province = this.item.location || ''

      const lines = [
        this.item.titleObj?.title
          ? `标题:${this.item.titleObj.rawTitle || this.item.titleObj.title}`
          : '',
        this.item.url ? `链接:${this.item.url}` : '',
        `摘要:${summary}`,
        this.item.source ? `来源:${this.item.source}` : '',
        this.item.author ? `作者:${this.item.author}` : '',
        this.item.publishTime ? `时间:${this.item.publishTime}` : '',
        authorId ? `作者ID:${authorId}` : '',
        this.sentiment ? `倾向性:${this.sentiment}` : '',
        typeof this.item.comments !== 'undefined'
          ? `评论数:${this.item.comments}`
          : '',
        typeof this.item.reposts !== 'undefined'
          ? `转发数:${this.item.reposts}`
          : '',
        keywords ? `涉及关键词:${keywords}` : '',
        `认证类型:${verifyType}`,
        typeof this.item.fansCount !== 'undefined'
          ? `粉丝数:${this.item.fansCount}`
          : '',
        province ? `涉事省份:${province}` : ''
      ]
        .filter(Boolean)
        .join('\n')

      navigator.clipboard
        .writeText(lines)
        .then(() => {
          this.$message.success('结构化信息已复制到剪切板')
        })
        .catch(() => {
          this.$message.error('复制失败，请手动复制')
        })
    },
    handleEditSentiment(val) {
      this.$emit('change-sentiment', {
        id: this.item.id,
        sentiment: val
      })
    },
    handleEditAiSentiment(val) {
      this.$emit('change-ai-sentiment', {
        id: this.item.uniqueId,
        value: val
      })
    },
    getFeatureReason(reason) {
      const reasonDict = this.dict.type.negative_public_opinion_tag.find(
        (item) => String(item.value) === String(reason)
      )
      return reasonDict ? reasonDict.label : ''
    },
    openDetail(item) {
      // 触发事件，通知父组件去标记已读
      this.$emit('mark-read', item.uniqueId)

      // 跳转逻辑
      // const dicts = this.dict && this.dict.type
      //   ? {
      //     filter_sentiment: this.dict.type.filter_sentiment,
      //     negative_public_opinion_tag: this.dict.type.negative_public_opinion_tag,
      //     ai_public_opinion_type: this.dict.type.ai_public_opinion_type,
      //     content_publish_type: this.dict.type.content_publish_type
      //   }
      //   : {};
      // const detailData = { ...item, dicts };
      // localStorage.setItem('yq_detail_item', JSON.stringify(detailData));
      // this.$router.push(`/yqmonitor/detail/${item.uniqueId}`)
      localStorage.removeItem('yq_detail_item')
      window.open(`/yqmonitor/detail/${item.uniqueId}`, '_blank')
    },
    handleExpandSimilar() {
      if (this.item.similarGroupId) {
        this.$emit('expand-similar', {
          similarGroupId: this.item.similarGroupId,
          representativeArticle: this.item
        })
      } else {
        this.$message.warning('此文章没有关联的相似组信息。')
      }
    },
    handleTitleClick(item) {
      // 如果有文本被选中，则不触发openDetail
      const selection = window.getSelection()
      if (selection.toString().length > 0) {
        return
      }
      // 没有选中文本时，打开详情
      this.openDetail(item)
    },
    handleWarning() {
      window.open(
        `/yqwarning/sender?warningId=${this.item.uniqueId}`,
        '_blank'
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.article-container {
  background: #fff;
  border-radius: 4px;
}

.item-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;

  .item-index {
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #f5f7fa;
    border-radius: 4px;
    margin-right: 12px;
    color: #909399;
    font-size: 14px;
  }

  .el-tag {
    margin-right: 8px;
  }

  .item-title {
    flex: 1;
    font-size: 18px;
    font-weight: 600;
    color: #303133;
    margin-right: 16px;
    overflow: hidden;
    text-decoration: none;

    &.is-read {
      color: #909399;
    }

    &:hover {
      color: #409eff;
    }
  }

  .item-stats {
    display: flex;
    gap: 16px;
    color: #909399;
    font-size: 16px;

    span {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .similar-count-badge {
      cursor: pointer;
    }
  }
}

.item-content {
  margin-bottom: 12px;
  cursor: pointer;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .info-group {
    display: flex;
    align-items: center;
    gap: 16px;
    font-size: 14px;
    color: #909399;

    .fans {
      color: #909399;
    }

    .platform {
      color: #909399;
      cursor: pointer;
    }

    .location {
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      &:hover {
        color: #409eff;
      }
    }

    .sentiment-tag {
      color: #909399;
    }

    .author-info {
      display: flex;
      align-items: center;
      color: #909399;

      .author-link {
        color: #909399;
        text-decoration: none;

        &:hover {
          color: #409eff;
        }
      }
    }
  }

  .item-icons {
    display: flex;
    align-items: center;
    gap: 8px;

    .warning-btn {
      background: #cce2ff;
      color: #e4393c;
      font-weight: bold;
      border-radius: 4px;
      padding: 6px 12px;
      border: none;
      transition: background 0.2s;

      &:hover {
        background: #b3d4fc;
        color: #d32f2f;
      }
    }
  }
}

.item-footer_last {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 8px;

  .hit-word_wrap {
    display: flex;
    column-gap: 12px;
    align-items: center;
    line-height: 1.5;

    .ai-feature-details {
      display: flex;
      column-gap: 20px;

      .feature-item {
        display: flex;
        align-items: center;
        font-size: 14px;

        .feature-reason {
          color: #909399;
          margin-right: 8px;
          flex-shrink: 0;
        }

        .feature-hitword {
          color: #f10a0a;
          word-break: break-all;
        }
      }
    }

    .hitword {
      color: #909399;
      font-size: 14px;

      .hitword-label {
        color: #909399;
      }

      .hitword-content {
        color: #f10a0a;
      }
    }
  }

  .time-wrap {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #909399;
    column-gap: 20px;
    white-space: nowrap;
  }
}

.main-content {
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.ocr-block,
.asr-block {
  display: flex;
  align-items: flex-start;
  margin-top: 12px;
  border-radius: 4px;
}

.ocr-content,
.asr-content {
  font-size: 16px;
  color: #333;
}

.ocr-tag {
  margin-right: 8px;
}

.asr-tag {
  margin-right: 8px;
}

.origin-inline {
  background: #fcedec;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
}

.origin-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  background: #e0e0e0;
  object-fit: cover;
  flex-shrink: 0;
}

.origin-text {
  display: block;
  flex: 1;
  min-width: 0;
  word-break: break-all;
}

.origin-nickname {
  color: #409eff;
  font-size: 15px;
  max-width: 100px;
  vertical-align: middle;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}

.origin-main-content {
  color: #333;
  font-size: 16px;
  vertical-align: middle;
  word-break: break-all;
}

.user-svg-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  background: #e0e0e0;
  display: inline-block;
  vertical-align: middle;
}

.read-status {
  display: inline-block;
  background: #aeb2b7;
  color: #fff;
  border-radius: 4px;
  font-size: 14px;
  padding: 2px 8px;
  margin-right: 8px;
}

.collect-time-highlight {
  border: 4px solid #b3cfff;
  border-radius: 20px;
  padding: 2px 12px;
  background: #f6faff;
  transition: box-shadow 0.2s;
  box-shadow: 0 2px 8px 0 #e3eefd;
}

.is-key-item {
  outline: 3px solid #ff9900;
  border-radius: 4px;
}
</style>

<style>
.hitword-highlight {
  color: #e4393c;
  background: #fffbe6;
  padding: 0 2px;
  border-radius: 2px;
  font-weight: bold;
}

.hitword-highlight-specail {
  color: #e47070f5 !important;
  font-weight: 600;
}

.custom-tag {
  display: inline-block;
  font-size: 16px;
  border-width: 1px;
  border-style: solid;
  border-radius: 4px;
  white-space: nowrap;
  margin-right: 8px;
  padding: 0 2px;
}

.zw {
  font-size: 12px;
}

.custom-tag-content {
  background-color: #e8f4ff;
  border-color: #d1e9ff;
  color: #1890ff;
}

.custom-tag-warning {
  background-color: #fff8e6;
  border-color: #fff1cc;
  color: #ffba00;
}

.custom-tag-success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
  color: #67c23a;
}

.avatar-img {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  vertical-align: -3px;
  margin-right: 2px;
}
</style>
