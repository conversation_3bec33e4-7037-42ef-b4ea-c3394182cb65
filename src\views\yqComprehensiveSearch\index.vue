<template>
  <div class="comprehensive-search-page" ref="searchPageRef">
    <!-- 顶部居中的搜索栏 -->
    <div class="top-search-bar">
      <el-input v-model="searchParams.word" placeholder="请输入关键词，空格表示与(AND)，| 表示或(OR)，a__b 表示短语" size="large"
        style="width: 600px" clearable @keyup.enter.native="fetchData(true)">
      </el-input>
      <el-button type="primary" size="large" icon="el-icon-search" @click="fetchData(true)">查询</el-button>
      <el-button size="large" icon="el-icon-refresh" @click="resetFilters">重置</el-button>
    </div>
    <!-- 筛选项容器 -->
    <div class="filter-container">
      <!-- 原有的所有 .filter-row 都移动到这里 -->
      <!-- 原来的 .filter-panel 的内容，除了关键词输入和操作按钮行 -->

      <!-- 信息来源 -->
      <div class="filter-row">
        <div class="filter-label">信息来源</div>
        <div class="filter-control">
          <el-button :type="isAllPlatformsSelected ? 'primary' : 'default'" size="small"
            @click="handlePlatformClick('all')">
            全选({{
              summaryData && summaryData.platform
                ? summaryData.platform.all
                : 0
            }})
          </el-button>
          <el-button v-for="platform in platformOptions" :key="platform.key" :type="searchParams.platform.includes(platform.key)
            ? 'primary'
            : 'default'
            " size="small" @click="handlePlatformClick(platform.key)">
            {{ platform.name }}({{
              summaryData &&
                summaryData.platform &&
                summaryData.platform[platform.name]
                ? summaryData.platform[platform.name]
                : 0
            }})
          </el-button>
        </div>
      </div>
      <!-- 监测时间 -->
      <div class="filter-row">
        <div class="filter-label">监测时间</div>
        <div class="filter-control">
          <el-button :type="searchParams.date === 'today' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('today')">今日</el-button>
          <el-button :type="searchParams.date === 'yesterday' ? 'primary' : 'default'
            " size="small" @click="handleDateClick('yesterday')">昨日</el-button>
          <el-button :type="searchParams.date === '24h' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('24h')">24h</el-button>
          <el-button :type="searchParams.date === '3d' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('3d')">近3日</el-button>
          <el-button :type="searchParams.date === '7d' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('7d')">近7日</el-button>
          <el-button :type="searchParams.date === '30d' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('30d')">近30日</el-button>
          <el-button :type="searchParams.date === 'custom' ? 'primary' : 'default'" size="small"
            @click="handleDateClick('custom')">自定义①</el-button>
          <el-date-picker v-if="isCustomDate" v-model="searchParams.defineDateRange" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" size="small" style="width: 350px">
          </el-date-picker>
        </div>
      </div>
      <!-- 信息属性 -->
      <div class="filter-row">
        <div class="filter-label">信息属性</div>
        <div class="filter-control">
          <el-button :type="isAllSentimentsSelected ? 'primary' : 'default'" size="small"
            @click="handleSentimentClick('all')">全部</el-button>
          <el-button v-for="senti in sentimentOptions" :key="senti" :type="searchParams.sentiment.includes(senti) ? 'primary' : 'default'
            " size="small" @click="handleSentimentClick(senti)">{{ senti }}</el-button>
        </div>
      </div>
      <!-- 相似合并 -->
      <div class="filter-row">
        <div class="filter-label">相似合并</div>
        <div class="filter-control">
          <el-button :type="searchParams.merge === 1 ? 'primary' : 'default'" size="small"
            @click="handleMergeClick(1)">开启</el-button>
          <el-button :type="searchParams.merge === 0 ? 'primary' : 'default'" size="small"
            @click="handleMergeClick(0)">关闭</el-button>
        </div>
      </div>
      <!-- 信息类型 -->
      <div class="filter-row">
        <div class="filter-label">信息类型</div>
        <div class="filter-control">
          <el-button :type="isAllOriginalsSelected ? 'primary' : 'default'" size="small"
            @click="handleOriginalClick('all')">全部</el-button>
          <el-button v-for="orig in originalOptions" :key="orig.key" :type="searchParams.original.includes(orig.key)
            ? 'primary'
            : 'default'
            " size="small" @click="handleOriginalClick(orig.key)">{{ orig.name }}</el-button>
        </div>
      </div>
      <!-- 信息浏览 -->
      <div class="filter-row">
        <div class="filter-label">信息浏览</div>
        <div class="filter-control">
          <el-button :type="searchParams.is_read === 0 ? 'primary' : 'default'" size="small"
            @click="handleReadStatusClick(0)">全部</el-button>
          <el-button :type="searchParams.is_read === 1 ? 'primary' : 'default'" size="small"
            @click="handleReadStatusClick(1)">已读</el-button>
          <el-button :type="searchParams.is_read === 2 ? 'primary' : 'default'" size="small"
            @click="handleReadStatusClick(2)">未读</el-button>
        </div>
      </div>

      <!-- "确认" 按钮，作用等同于查询 -->
      <div class="filter-actions">
        <el-button type="primary" size="small" @click="fetchData(true)">确定</el-button>
      </div>
    </div>
    <!-- 结果列表居中容器 -->
    <div class="main-content-area">
      <div class="results-list-container" v-loading="loading">
        <div v-if="!loading && results.length === 0" class="no-data">
          <i class="el-icon-document"></i>
          <p>暂无数据</p>
        </div>
        <template v-else-if="!loading && results.length > 0">
          <ResultItem v-for="item in results" :key="item.unique_id" :item="item"
            :highlight-words="item.highlight_words" />
        </template>
      </div>
    </div>
    <!--固定底部操作栏 -->
    <ActionBar :total="pagination.total" :current-page.sync="pagination.currentPage"
      :page-size.sync="pagination.pageSize" :sort-type.sync="searchParams.sort" :sort-options="sortOptions"
      @page-change="handlePageChange" @size-change="handleSizeChange" @sort-change="handleSortChange" />

    <!-- 滚动按钮 -->
    <ScrollButtons v-if="scrollContainer" :scroll-container="scrollContainer" />

  </div>
</template>

<script>
import ResultItem from "./components/ResultItem.vue";
import ActionBar from "./components/ActionBar.vue";
import ScrollButtons from "@/views/yqmonitor/components/scrollButtons.vue";
import { getSearchList, getToken } from "@/api/yqComprehensiveSearch/index.js";

export default {
  name: "YqComprehensiveSearch",
  components: {
    ResultItem,
    ActionBar,
    ScrollButtons,
  },
  data() {
    // 定义筛选条件的常量配置
    const platformOptions = [
      { name: "抖音", key: "douyin" },
      { name: "微博", key: "weibo" },
      { name: "微信", key: "weixin" },
      { name: "头条", key: "toutiao" },
      { name: "小红书", key: "xiaohongshu" },
      { name: "网页", key: "web" },
      { name: "客户端", key: "app" },
      { name: "论坛", key: "bbs" },
      { name: "数字报", key: "enews" },
      { name: "境外", key: "jingwai" },
      { name: "其他视频", key: "shipin" },
    ];
    const sentimentOptions = ["敏感", "非敏感", "中性"];
    const originalOptions = [
      { name: "原创", key: 0 },
      { name: "转发", key: 1 },
      { name: "评论", key: 2 },
    ];

    const sortOptions = [
      { label: "最新发文", value: "time_desc" },
      { label: "最早发文", value: "time_asc" },
      { label: "最新入库", value: "create_desc" },
      { label: "最早入库", value: "create_asc" },
      { label: "相关度最高", value: "score" },
      { label: "粉丝量最高", value: "fans_desc" },
      { label: "互动量最高", value: "respond_desc" },
      { label: "点赞量最高", value: "like_desc" },
      { label: "评论量最高", value: "comment_desc" },
      { label: "转发量最高", value: "share_desc" },
    ];

    // 定义默认搜索参数，用于初始化和重置
    const defaultSearchParams = {
      word: "",
      platform: platformOptions.map((p) => p.key), // 默认全选
      date: "24h",
      defineDateRange: [],
      sentiment: [...sentimentOptions], // 默认全选
      merge: 0, // 默认关闭
      original: originalOptions.map((o) => o.key), // 默认全选
      is_read: 0, // 默认全部
      sort: 'time_desc',
    };

    return {
      token: "", // 新增 token 变量
      searchParams: JSON.parse(JSON.stringify(defaultSearchParams)),
      defaultSearchParams, // 保存一份用于重置
      platformOptions,
      sentimentOptions,
      originalOptions,
      sortOptions,
      isCustomDate: false, // 控制自定义时间选择器的显示

      loading: false,
      results: [],
      summaryData: null,
      pagination: {
        currentPage: 1,
        pageSize: 50,
        total: 0,
      },
      scrollContainer: null, // 滚动容器实例
    };
  },
  computed: {
    isAllPlatformsSelected() {
      return this.searchParams.platform.length === this.platformOptions.length;
    },
    isAllSentimentsSelected() {
      return (
        this.searchParams.sentiment.length === this.sentimentOptions.length
      );
    },
    isAllOriginalsSelected() {
      return this.searchParams.original.length === this.originalOptions.length;
    },
    totalCount() {
      return this.summaryData && this.summaryData.post_count
        ? this.summaryData.post_count.all
        : 0;
    },
    totalPages() {
      return Math.ceil(this.pagination.total / this.pagination.pageSize);
    },
  },
  created() {
    // 获取 token
    getToken()
      .then((res) => {
        console.log("getToken", res);
        if (res && res.code === 200 && res.data) {
          this.token = res.data.xiaoyToken;
        } else {
          this.token = "";
        }
      })
      .catch(() => {
        this.token = "";
      });
  },
  mounted() {
    this.scrollContainer = this.$refs.searchPageRef;
    document.body.style.overflowY = 'hidden'
  },
  beforeDestroy() {
    document.body.style.overflowY = ''
  },
  methods: {
    // --- 筛选条件点击事件 ---
    handlePlatformClick(platformKey) {
      if (platformKey === "all") {
        this.searchParams.platform = this.isAllPlatformsSelected
          ? []
          : this.platformOptions.map((p) => p.key);
      } else {
        const index = this.searchParams.platform.indexOf(platformKey);
        if (index > -1) {
          this.searchParams.platform.splice(index, 1);
        } else {
          this.searchParams.platform.push(platformKey);
        }
      }
    },
    handleDateClick(dateType) {
      this.searchParams.date = dateType;
      this.isCustomDate = dateType === "custom";
      if (!this.isCustomDate) {
        this.searchParams.defineDateRange = [];
      }
    },
    handleSentimentClick(sentiment) {
      if (sentiment === "all") {
        this.searchParams.sentiment = this.isAllSentimentsSelected
          ? []
          : [...this.sentimentOptions];
      } else {
        const index = this.searchParams.sentiment.indexOf(sentiment);
        if (index > -1) {
          this.searchParams.sentiment.splice(index, 1);
        } else {
          this.searchParams.sentiment.push(sentiment);
        }
      }
    },
    handleMergeClick(value) {
      this.searchParams.merge = value;
    },
    handleOriginalClick(originalKey) {
      if (originalKey === "all") {
        this.searchParams.original = this.isAllOriginalsSelected
          ? []
          : this.originalOptions.map((o) => o.key);
      } else {
        const index = this.searchParams.original.indexOf(originalKey);
        if (index > -1) {
          this.searchParams.original.splice(index, 1);
        } else {
          this.searchParams.original.push(originalKey);
        }
      }
    },
    handleReadStatusClick(status) {
      this.searchParams.is_read = status;
    },
    resetFilters() {
      this.searchParams = JSON.parse(JSON.stringify(this.defaultSearchParams));
      this.isCustomDate = false;
      // 重置分页状态
      this.pagination.currentPage = 1;
      this.pagination.pageSize = 50;
    },
    // 排序变化处理
    handleSortChange(newSortType) {
      this.searchParams.sort = newSortType;
      this.fetchData(true); // 排序变化，从第一页开始查
    },
    // --- 数据获取与处理 ---

    /**
     * 格式化日期对象为 'YYYY-MM-DD HH:mm' 字符串
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDateForApi(date) {
      if (!date) return "";
      const d = new Date(date);
      const pad = (n) => (n < 10 ? "0" + n : String(n));
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(
        d.getDate()
      )} ${pad(d.getHours())}:${pad(d.getMinutes())}`;
    },

    async fetchData(resetPage = false) {
      // 1. 调用新方法处理关键词
      const processedWord = this.processKeywords(this.searchParams.word);

      if (!processedWord) {
        this.$message.error("请输入有效的关键词");
        return;
      }

      // 如果是新的查询（不是分页切换），重置到第一页
      if (resetPage) {
        this.pagination.currentPage = 1;
      }

      this.loading = true;

      // 深拷贝一份参数用于API请求，避免修改原始searchParams
      const paramsForApi = JSON.parse(JSON.stringify(this.searchParams));
      // 将处理后的关键词赋值给用于 API 请求的参数
      paramsForApi.word = processedWord;

      // 处理自定义时间，使用本地的formatDateForApi方法
      if (paramsForApi.date === "custom") {
        if (
          paramsForApi.defineDateRange &&
          paramsForApi.defineDateRange.length === 2
        ) {
          const startDate = this.formatDateForApi(
            paramsForApi.defineDateRange[0]
          );
          const endDate = this.formatDateForApi(
            paramsForApi.defineDateRange[1]
          );
          paramsForApi.date = `${startDate},${endDate}`;
          delete paramsForApi.defineDateRange;
        } else {
          this.$message.error("请选择自定义时间范围");
          this.loading = false;
          return;
        }
      } else {
        delete paramsForApi.defineDateRange;
      }

      // 定义后端必需的默认参数
      const requiredBaseParams = {
        // [最关键] 指定搜索范围，告诉后端去哪些字段里搜索关键词
        hit_field: [
          "content",
          "title",
          "cover_ocr",
          "ocr",
          "asr",
          "nickname",
          "poi",
          "user_location",
        ],
        // 其他在原版请求中存在的、代表“全选”的参数
        // 虽然界面上没有，但后端可能依赖它们进行过滤
        post_category: 0,
        media: [0, 1, 2],
        source_level: [
          "央级",
          "省级",
          "地市",
          "区县",
          "境外",
          "商业",
          "中小",
          "行业门户",
          "其他",
        ],
        verify: ["政务认证", "机构认证", "企业认证", "个人认证", "未认证"],
      };
      const listParams = {
        ...requiredBaseParams,
        ...paramsForApi,
        order: paramsForApi.sort,
        return_type: 1,
        page: this.pagination.currentPage,
        page_size: this.pagination.pageSize,
      };
      const summaryParams = {
        ...requiredBaseParams,
        ...paramsForApi,
        order: paramsForApi.sort,
        return_type: 2,
      };

      try {
        const [listRes, summaryRes] = await Promise.all([
          getSearchList(listParams, this.token),
          getSearchList(summaryParams, this.token),
        ]);
        if (listRes.code === 0 && listRes.data) {
          this.results = listRes.data.list.map((item, index) =>
            this.transformResultItem(item, index)
          );
          this.pagination.total = listRes.data.total;
        } else {
          this.results = [];
          this.pagination.total = 0;
          this.$message.error(listRes.message || "获取列表数据失败");
        }
        if (summaryRes.code === 0 && summaryRes.data) {
          this.summaryData = summaryRes.data;
        } else {
          this.summaryData = null;
          this.$message.error(summaryRes.message || "获取摘要数据失败");
        }
      } catch (error) {
        console.error("Failed to fetch search results:", error);
        this.$message.error("数据加载失败，请检查网络");
        this.results = [];
        this.summaryData = null;
        this.pagination.total = 0;
      } finally {
        this.loading = false;
      }
    },

    transformResultItem(item, index) {
      return {
        id:
          (this.pagination.currentPage - 1) * this.pagination.pageSize +
          index +
          1,
        unique_id: item.unique_id,
        unity_id: item.unity_id,
        author: item.nickname,
        avatar: item.avatar,
        tags: (item.hit_word || []).map((tag) => `${tag.word}(${tag.count})`),
        location: item.ip_location,
        summary: item.desc,
        mainContent: item.content,
        quote:
          item.original === "转发" && item.origin && item.origin.unique_id
            ? {
              author: item.origin.nickname,
              timestamp: item.origin.post_create_time,
              content: item.origin.content,
            }
            : null,
        original: item.original === "原创",
        like_count: item.like_count,
        comment_count: item.comment_count,
        share_count: item.share_count,
        view_count: item.view_count,
        source: item.platform_name,
        timestamp: item.post_create_time,
        isRead: item.is_read === 1, // 1: 已读, 2: 未读, 0: 全部
        readStatus: item.is_read, // 传递原始状态值用于更精确的判断
        sentiment: item.sentiment,
        highlight_words: (item.hit_word || []).map((h) => h.word),
      };
    },

    handlePageChange(newPage) {
      this.pagination.currentPage = newPage;
      this.fetchData();
    },

    handleSizeChange(newSize) {
      this.pagination.pageSize = newSize;
      this.pagination.currentPage = 1; // 切换每页条数时重置到第一页
      this.fetchData();
    },
    processKeywords(rawKeywords) {
      if (!rawKeywords || typeof rawKeywords !== "string") {
        return "";
      }

      // 预处理：去除首尾空格，并将用户输入的 & 符号统一成空格，便于后续处理
      let processedString = rawKeywords.trim().replace(/&/g, " ");

      // 按“或”运算符 | 分割字符串，对每个部分独立应用“与”逻辑
      const orParts = processedString.split("|").map((part) => part.trim());

      const finalParts = orParts.map((part) => {
        if (!part) return ""; // 跳过空部分

        // 规则 3: 优先处理带下划线的短语查询 (例如: a__b -> "a b")
        // 我们使用一个临时占位符来保护这些短语，防止其中的空格被替换成 &
        const phrases = part.match(/\S*__\S*/g) || [];
        const phrasePlaceholders = [];

        phrases.forEach((phrase, index) => {
          const placeholder = `__PHRASE_${index}__`;
          // 将 a__b 替换为 "a b"
          const realPhrase = `"${phrase.replace(/__/g, " ")}"`;
          // 先用占位符替换原始字符串中的 a__b
          part = part.replace(phrase, placeholder);
          phrasePlaceholders.push({ placeholder, realPhrase });
        });

        // 现在 part 中所有剩下的空格都代表“与”逻辑
        // 规则 1: 将所有单个或多个空格替换为单个 &
        // 例如 "王宝强  塌方" 会变成 "王宝强&塌方"
        // 使用 filter(Boolean) 移除因首尾空格产生的空数组元素
        let andProcessedPart = part.split(/\s+/).filter(Boolean).join("&");

        // 将之前保护的短语替换回来
        phrasePlaceholders.forEach(({ placeholder, realPhrase }) => {
          andProcessedPart = andProcessedPart.replace(placeholder, realPhrase);
        });

        return andProcessedPart;
      });

      // 规则 2: 将处理好的各个部分用 | 重新连接起来
      return finalParts.join("|");
    },
  },
};
</script>

<style lang="scss" scoped>
.comprehensive-search-page {
  height: 100%; // 确保能获取到滚动 ref
  background-color: #f5f7fa; // 增加背景色，让留白更明显
  padding-bottom: 80px; // 为底部固定栏留出空间
  overflow-y: scroll;
}

.top-search-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;
  background-color: #fff;
  border-bottom: 1px solid #e4e7ed;
  gap: 15px;
}

.filter-container {
  background: #fff;
  padding: 20px 30px 10px;
  margin: 20px auto;
  max-width: 1400px;
  border-radius: 4px;
  border: 1px solid #e4e7ed;

  .filter-row {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .filter-label {
      width: 80px;
      text-align: right;
      color: #606266;
      margin-right: 15px;
      flex-shrink: 0;
    }

    .filter-control {
      flex: 1;
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      align-items: center;
    }
  }

  .filter-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
    border-top: 1px dashed #dcdfe6;
    padding-top: 15px;
  }
}

.main-content-area {
  max-width: 1400px;
  margin: 0 auto;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  padding: 0 20px;
}

.results-list-container {
  min-height: 400px; // 保证在无数据时也有一定高度

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #909399;
    font-size: 14px;

    i {
      font-size: 48px;
      margin-bottom: 10px;
    }

    p {
      margin: 0;
    }
  }
}
</style>
