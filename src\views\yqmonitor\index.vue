<template>
  <div class="app-container" :key="$route.fullPath">
    <el-container>
      <side-menu />
      <main-content :module-type="moduleType"/>
    </el-container>
    <topic-drawer :visible.sync="localDrawerVisible" />
  </div>
</template>

<script>
import SideMenu from './components/SideMenu/index.vue'
import MainContent from './components/MainContent/index.vue'
import TopicDrawer from './components/topic/index.vue'
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'YQMonitor',
  components: {
    SideMenu,
    MainContent,
    TopicDrawer
  },
  data() {
    return {
      localDrawerVisible: false
    }
  },
  computed: {
    ...mapState('yqmonitorTopic', ['topicDrawerVisible']),
    moduleType() {
      const path = this.$route.path;
      if (path.startsWith('/followedEvents')) {
        return 'followedEvents';
      }
      if (path.startsWith('/targetMonitor')) {
        return 'targetMonitor';
      }
      return 'yqmonitor'; // 默认是舆情监控
    }
  },
  watch: {
    topicDrawerVisible: {
      immediate: true,
      handler(val) {
        this.localDrawerVisible = val
      }
    },
    localDrawerVisible(val) {
      this.SET_TOPIC_DRAWER_VISIBLE(val)
    },
    // 监听 moduleType 变化，设置对应的业务类型
    moduleType: {
      immediate: true,
      handler(newModuleType) {
        this.setBusinessTypeByModule(newModuleType)
      }
    },
    // 监听路由变化，重新渲染组件
    '$route'(to, from) {
      // 在这里可以添加自定义的重新渲染逻辑
      console.log('路由发生变化，从', from.path, '到', to.path)
      // 可以在这里执行特定的重新初始化逻辑
      this.$nextTick(() => {
        // 强制重新渲染
        this.$forceUpdate()
      })
    }
  },
  methods: {
    ...mapMutations('yqmonitorTopic', ['SET_TOPIC_DRAWER_VISIBLE']),
    // 根据 moduleType 设置对应的业务类型
    setBusinessTypeByModule(moduleType) {
      let businessType = 'yqmonitor-business' // 默认值
      
      switch (moduleType) {
        case 'yqmonitor':
          businessType = 'yqmonitor-business'
          break
        case 'followedEvents':
          businessType = 'followed-events-business'
          break
        case 'targetMonitor':
          businessType = 'target-monitor-business'
          break
        default:
          businessType = 'yqmonitor-business'
      }
      
      this.$store.commit('yqmonitorMenu/SET_CURRENT_BUSINESS_TYPE', businessType)
      console.log('设置业务类型:', businessType, '对应模块:', moduleType)
    }
  },
  mounted() {
    document.body.style.overflowY = 'hidden'
  },
  beforeDestroy() {
    document.body.style.overflowY = ''
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  height: 100%;
  background-color: #f5f7fa;
  padding: 0;
  .el-container {
    height: 100%;
  }
}
</style>
