<template>
  <div class="fixed-action-bar">
    <el-checkbox
      :indeterminate="checkedList.length > 0 && checkedList.length < contentList.length"
      :value="checkedList.length === contentList.length && contentList.length > 0"
      @change="val => $emit('check-all', val)"
    >全部</el-checkbox>
    <el-button @click="$emit('mark-read')">标已读</el-button>
    <el-dropdown v-hasPermi="['yqmonitor:editSentiment','followedEvents:editSentiment','targetMonitor:editSentiment']" @command="val => $emit('change-sentiment', val)">
      <el-button>修改倾向性</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in sentimentOptions.filter(item => item.value !== 'all')"
          :key="item.value"
          :command="item.value"
        >
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dropdown v-hasPermi="['yqmonitor:editAiSentiment','followedEvents:editAiSentiment','targetMonitor:editAiSentiment']" @command="val => $emit('change-ai-sentiment-type', val)">
      <el-button>修改AI舆情倾向性</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="item in aiSentimentOptions.filter(item => item.value !== 'all')"
          :key="item.value"
          :command="item.value"
        >
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-select
      v-model="localSelectedSortType"
      placeholder="排序方式"
      style="width: 160px; margin-right: 10px;"
      @change="val => $emit('sort-type-change', val)"
    >
      <el-option v-for="item in sortOptions" :key="item.label" :label="item.label" :value="item.value" />
    </el-select>
    <el-button v-hasPermi="['yqmonitor:delete','followedEvents:delete','targetMonitor:delete']" @click="handleDelete">删除</el-button>
    <el-button type="danger" @click="$emit('refresh')">刷新</el-button>
    <el-dropdown v-hasPermi="['yqmonitor:createWarning','followedEvents:createWarning','targetMonitor:createWarning']" trigger="hover" class="create-warning-btn" @command="handleCreateWarning">
      <el-button type="primary">新建预警</el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="report">早晚报</el-dropdown-item>
        <el-dropdown-item command="summary">汇总</el-dropdown-item>
        <el-dropdown-item command="normal">普通预警</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
// const IS_PRODUCTION = process.env.NODE_ENV === "production";

export default {
  name: 'FixedActionBar',
  props: {
    checkedList: Array,
    contentList: Array,
    sentimentOptions: Array,
    aiSentimentOptions: Array,
    selectedSortType: String,
    sortOptions: Array,
  },
  data() {
    return {
      localSelectedSortType: this.selectedSortType
      // isProduction: IS_PRODUCTION,
    }
  },
  watch: {
    selectedSortType(val) {
      this.localSelectedSortType = val
    }
  },
  methods: {
    handleDelete() {
      this.$emit('delete')
    },
    handleCreateWarning(type) {
      console.log(type)
      window.open(`/yqwarning/sender?mode=${type}`, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.fixed-action-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 16px 20px;
  background: #fff;
  display: flex;
  align-items: center;
  border-top: 1px solid #ebeef5;
  z-index: 10;
  gap: 10px;

  .create-warning-btn {
    margin-left: auto;
  }
}
</style>
